package com.chief.maven.plugin.mojo;

import com.chief.toolkit.fom.ObjectModel;
import com.chief.toolkit.fom.datatype.BasicType;
import com.chief.toolkit.fom.datatype.EnumeratedType;
import com.chief.toolkit.fom.datatype.IDatatype;
import com.chief.toolkit.fom.datatype.SimpleType;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

import static com.chief.maven.plugin.mojo.GenerateModel.BASE_PACKAGE;
import static com.chief.maven.plugin.mojo.GenerateModel.fomService;

public record DataTypeInfo(IDatatype datatype, ObjectModel model) {

    private static Map<String, String> fastMapping = new HashMap<>();


    static {
        try (InputStream stream = DataTypeInfo.class.getClassLoader().getResourceAsStream("dataTypeMapping.txt")) {
            BufferedReader reader = new BufferedReader(new InputStreamReader(stream));

            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                String[] split = line.split("=");
                if (split.length == 2) {
                    fastMapping.put(split[0].trim(), split[1].trim());
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static boolean isBasicJavaType(String dataTypeName) {
        String fastMatch = fastMapping.get(dataTypeName);
        if (fastMatch != null) {
            return true;
        }

        IDatatype d = fomService.getDatatype(dataTypeName);
        return d instanceof BasicType || d instanceof SimpleType || d instanceof EnumeratedType;
    }

    public static boolean isBasicJavaType(IDatatype datatype) {
        String fastMatch = fastMapping.get(datatype.getName());
        if (fastMatch != null) {
            return true;
        }

        return datatype instanceof BasicType
                || datatype instanceof SimpleType
                || datatype instanceof EnumeratedType;
    }

    public String pkg() {
        return getPackagePrefix(model.getModuleName());
    }

    public String pkgDTO() {
        return getPackagePrefix(model.getModuleName()) + ".dto";
    }

    public String getJavaTypeName(String dataTypeName) {
        String fastMatch = fastMapping.get(dataTypeName);
        if (fastMatch != null) {
            return fastMatch;
        }
        try {
            IDatatype d = fomService.getDatatype(dataTypeName);

            switch (d) {
                case BasicType basic -> {
                    return getBasicName(basic);
                }
                case SimpleType simpleType -> {
                    return getJavaTypeName(simpleType.getRepresentation().getName());
                }
                case EnumeratedType enumeratedType -> {
                    return Integer.class.getSimpleName();
                }
                default -> {
                    return getPackagePrefix(d.getModuleName()) + "." + d.getName();
                }
            }
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    private String getBasicName(BasicType basic) {
        if (basic.getName().toLowerCase().contains("integer")) {
            if (basic.getSize() > 32) {
                return Long.class.getSimpleName();
            } else {
                return Integer.class.getSimpleName();
            }
        }
        if (basic.getName().toLowerCase().contains("float")) {
            if (basic.getSize() > 32) {
                return Double.class.getSimpleName();
            } else {
                return Float.class.getSimpleName();
            }
        }
        if (basic.getName().toLowerCase().contains("octet")) {
            return Integer.class.getSimpleName();
        }
        if (basic.getName().toLowerCase().contains("string")) {
            return String.class.getSimpleName();
        }
        if (basic.getName().toLowerCase().contains("boolean")) {
            return Boolean.class.getSimpleName();
        }
        throw new RuntimeException("未知的基础类型.");
    }

    public String getJavaTypeNameDTO(String dataTypeName) {
        String fastMatch = fastMapping.get(dataTypeName);
        if (fastMatch != null) {
            return fastMatch;
        }

        IDatatype d = fomService.getDatatype(dataTypeName);

        switch (d) {
            case BasicType basic -> {
                return getBasicName(basic);
            }
            case SimpleType simpleType -> {
                return getJavaTypeNameDTO(simpleType.getRepresentation().getName());
            }
            case EnumeratedType enumeratedType -> {
                return Integer.class.getSimpleName();
            }
            default -> {
                return getPackagePrefix(d.getModuleName()) + ".dto." + d.getName() + "DTO";
            }
        }
    }

    public String getNow() {
        LocalDateTime now = LocalDateTime.now();
        // 自定义格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return now.format(formatter);
    }

    public String getSemantics(String dataTypeName, String prefix) {
        String semantics = model.getDatatype(dataTypeName).getSemantics();
        if (semantics == null) {
            return "";
        }
        semantics = semantics.replace("\r\n", "\r\n" + prefix);
        semantics = semantics.replace("\n", "\r\n" + prefix);
        return semantics;
    }

    public String formatSemantics(String semantics, String prefix) {
        if (semantics == null) {
            return "";
        }
        semantics = semantics.replace("\r\n", "\r\n" + prefix);
        semantics = semantics.replace("\n", "\r\n" + prefix);
        return semantics;
    }

    private String getPackagePrefix(String moduleName) {
        return String.format("%s.%s.data_type", BASE_PACKAGE, moduleName);
    }

}
