package ${pkgDTO()};

import java.util.ArrayList;
import java.util.List;
import java.io.Serializable;
import lombok.Data;
/**
* ${formatSemantics(datatype().getSemantics(), "* ")}
*
* <AUTHOR>
* @program: csim
* @date ${getNow()}
*/
public record ${datatype().getName()}DTO (
<#list datatype().alternatives as field>
    ${getJavaTypeNameDTO(field.datatype.name)} ${field.name}<#if field_has_next>,</#if>
</#list>
){}
