package com.chief.engine.core.model;


import com.chief.engine.core.enums.EngineStatus;
import com.chief.engine.core.event.AddChildMsg;
import com.chief.engine.core.event.AddModelMsg;
import com.chief.engine.core.event.TickMsg;
import com.chief.engine.core.util.ObjectHandlerUtils;
import com.chief.model.api.annotation.ThisInvoke;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ScheduledThreadPoolExecutor;

import static com.chief.engine.core.enums.EngineStatus.*;

/**
 * 调度模型
 */
@Slf4j
public class RootModel extends AtomicModel {

    public static final String ROOT_MODEL_ID = "-1";
    static long physicalStartTime;
    private final Map<String, AtomicModel> models = new HashMap<>();
    private EngineStatus status = created;
    private double speed = -1;
    private long physicalTime;
    private long simEndTime;


    public RootModel(long simStartTime, long simEndTime) {
        super(null, "-1", simStartTime);
        models.put(ROOT_MODEL_ID, this);

        new ScheduledThreadPoolExecutor(1);
        this.simEndTime = simEndTime;
    }

    private void addObject(AddModelMsg event) {
        AtomicModel parent = models.get(event.pid());
        Assert.notNull(parent, () -> "父节点[" + event.pid() + "] 不存在");

        AtomicModel child;
        if (parent instanceof OperationModel<?>) {
            child = new OperationModel<>(parent, parent, event.id(), simStartTime, ObjectHandlerUtils.getModelObjectHandler(event.object()), event.object());
        } else {
            child = new OperationModel<>(parent, event.id(), simStartTime, ObjectHandlerUtils.getModelObjectHandler(event.object()), event.object());
        }
        AtomicModel old = models.put(event.id(), child);
        Assert.isNull(old, () -> "[" + event.id() + "] 重复");
        parent.send(new AddChildMsg(child));
    }

    public void start(long currentTime) {
        log.info("开始推演: 共{}个实体", models.size());
        status = running;
        physicalStartTime = System.currentTimeMillis();

        send(new TickMsg(currentTime));
    }


    @Override
    protected void tick(TickMsg event) {

        this.physicalTime = System.currentTimeMillis();
        if (event.logicTime() > simEndTime) {
            status = stopped;
            long cost = System.currentTimeMillis() - physicalStartTime;
            log.info("cost: {}   倍速：{}", cost, (simEndTime - simStartTime) / 1.0 / cost);
            return;
        }
        super.tick(event);
    }

    @SneakyThrows
    @Override
    @ThisInvoke
    protected void onTickComplete() {
        if (running.equals(this.status)) {
            long next = this.next();
            if (speed > 0) {
                long delta = next - getCurrentTime();
                long nextPhysicalTime = this.physicalTime + (long) (delta / speed);
                long nowTime = System.currentTimeMillis();
                if (nowTime < nextPhysicalTime) {
                    long hh = nextPhysicalTime - nowTime;
                    Thread.sleep(hh);
                    send(new TickMsg(next));
                } else {
                    send(new TickMsg(next));
                }
            } else {
                send(new TickMsg(next));
            }

        }
    }

    @Override
    protected boolean onMessage(Object message) {
        switch (message) {
            case AddModelMsg e -> this.addObject(e);
            default -> super.onMessage(message);
        }
        return true;
    }
}
