package com.chief.maven.plugin.mojo;

import com.chief.toolkit.fom.ACMetadata;
import com.chief.toolkit.fom.OCMetadata;
import com.chief.toolkit.fom.ObjectModel;
import com.chief.toolkit.fom.datatype.IDatatype;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Set;

import static com.chief.maven.plugin.mojo.GenerateModel.BASE_PACKAGE;
import static com.chief.maven.plugin.mojo.GenerateModel.fomService;

public record ObjectInfo(OCMetadata objectClass, ObjectModel model) {

    public String pkg() {
        return getPackagePrefix(model.getModuleName());
    }

    public String pkgDTO() {
        return getPackagePrefix(model.getModuleName()) + ".dto";
    }

    public String getJavaTypeName(String dataTypeName) {
        DataTypeInfo dataTypeInfo = new DataTypeInfo(null, model);
        return dataTypeInfo.getJavaTypeName(dataTypeName);
    }

    public String getJavaTypeNameDTO(String dataTypeName) {
        DataTypeInfo dataTypeInfo = new DataTypeInfo(null, model);
        return dataTypeInfo.getJavaTypeNameDTO(dataTypeName);
    }

    public boolean isBasicJavaType(String dataTypeName) {
        return DataTypeInfo.isBasicJavaType(dataTypeName);
    }

    public String getNow() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return now.format(formatter);
    }

    public String formatSemantics(String semantics, String prefix) {
        if (semantics == null || semantics.trim().isEmpty()) {
            return "";
        }
        semantics = semantics.replace("\r\n", "\r\n" + prefix);
        semantics = semantics.replace("\n", "\r\n" + prefix);
        return semantics;
    }

    public String getParentClassName() {
        OCMetadata parent = objectClass.getParent();
        if (parent == null || "HLAobjectRoot".equals(parent.getName())) {
            return null;
        }
        return parent.getName();
    }

    public String getParentClassFullName() {
        OCMetadata parent = objectClass.getParent();
        if (parent == null || "HLAobjectRoot".equals(parent.getName())) {
            return null;
        }
        return getPackagePrefix(parent.getModuleName()) + "." + parent.getName();
    }

    public String getParentClassFullNameDTO() {
        OCMetadata parent = objectClass.getParent();
        if (parent == null || "HLAobjectRoot".equals(parent.getName())) {
            return null;
        }
        return getPackagePrefix(parent.getModuleName()) + ".dto." + parent.getName() + "DTO";
    }

    public List<ACMetadata> getDeclaredAttributes() {
        return objectClass.getDeclaredAttributes();
    }

    public List<ACMetadata> getAllAttributes() {
        return objectClass.getAllAttributes();
    }

    public boolean hasParent() {
        OCMetadata parent = objectClass.getParent();
        return parent != null && !"HLAobjectRoot".equals(parent.getName());
    }

    public boolean hasDeclaredAttributes() {
        List<ACMetadata> attributes = objectClass.getDeclaredAttributes();
        return attributes != null && !attributes.isEmpty();
    }

    public String getClassName() {
        return objectClass.getName();
    }

    public String getSemantics() {
        return objectClass.getSemantics();
    }

    private String getPackagePrefix(String moduleName) {
        return String.format("%s.%s.object", BASE_PACKAGE, moduleName);
    }
}
