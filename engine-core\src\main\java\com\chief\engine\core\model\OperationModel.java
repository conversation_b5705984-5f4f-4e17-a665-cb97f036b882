package com.chief.engine.core.model;

import com.chief.model.api.ModelContext;
import com.chief.model.api.ModelObject;
import com.chief.model.api.ModelObjectHandler;
import com.chief.toolkit.support.SFunction;
import jakarta.annotation.Nullable;

public class OperationModel<T extends ModelObject> extends AtomicModel implements ModelContext {
    /**
     * 业务模型处理器
     */
    private final ModelObjectHandler<T> handler;

    private final T modelObject;

    protected OperationModel(@Nullable AtomicModel parent, String id, long simStartTime,
                             ModelObjectHandler handler, T modelObject) {
        this(parent, null, id, simStartTime, handler, modelObject);
    }

    protected OperationModel(@Nullable AtomicModel parent, @Nullable Actor executorParent, String id, long simStartTime,
                             ModelObjectHandler handler, T modelObject) {
        super(parent, executorParent, id, simStartTime);
        this.handler = handler;
        this.modelObject = modelObject;
        this.handler.setContext(this);
    }


    @Override
    protected void update(long delta) {
        if (handler != null) {
            handler.update(delta);
        }
    }

    @Override
    public T getModelObject() {
        return modelObject;
    }

    @Override
    public void subAttribute(SFunction attrGetter) {

    }

    @Override
    public ModelContext getParentModelContext() {
        if (this.parent instanceof ModelContext parentModelContext) {
            return parentModelContext;
        }
        return null;
    }

    @Override
    protected void onJoinSimulation(long logicTime) {
        handler.onJoinSimulation(logicTime);
    }
}
