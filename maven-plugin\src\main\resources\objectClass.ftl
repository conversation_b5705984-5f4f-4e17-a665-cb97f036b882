package ${pkg()};

<#if hasParent()>
import ${getParentClassFullName()};
</#if>
import java.io.Serializable;
import lombok.Data;
<#if hasParent()>
import lombok.EqualsAndHashCode;
</#if>

/**
* ${formatSemantics(getSemantics(), "* ")}
*
* <AUTHOR>
* @program: csim
* @date ${getNow()}
*/
@Data
<#if hasParent()>
@EqualsAndHashCode(callSuper = true)
public class ${getClassName()} extends ${getParentClassName()} implements Serializable {
<#else>
public class ${getClassName()} implements Serializable {
</#if>

<#if hasDeclaredAttributes()>
<#list getDeclaredAttributes() as attribute>
    <#if attribute.getSemantics()?? && attribute.getSemantics()?has_content>
    /**
     * ${formatSemantics(attribute.getSemantics(), "     * ")}
     */
    </#if>
    private ${getJavaTypeName(attribute.datatype.name)} ${attribute.name};

</#list>
</#if>
    /**
     * 转换为DTO对象
     */
    public ${getClassName()}DTO toDTO() {
        ${getClassName()}DTO dto = new ${getClassName()}DTO();
<#if hasParent()>
        // 复制父类属性
        super.copyToDTO(dto);
</#if>
<#if hasDeclaredAttributes()>
        // 复制当前类属性
<#list getDeclaredAttributes() as attribute>
        dto.set${attribute.name?cap_first}(this.${attribute.name}<#if !isBasicJavaType(attribute.datatype.name)> != null ? this.${attribute.name}.toDTO() : null</#if>);
</#list>
</#if>
        return dto;
    }

<#if hasParent()>
    /**
     * 将当前对象属性复制到DTO对象
     */
    protected void copyToDTO(${getClassName()}DTO dto) {
        super.copyToDTO(dto);
<#if hasDeclaredAttributes()>
<#list getDeclaredAttributes() as attribute>
        dto.set${attribute.name?cap_first}(this.${attribute.name}<#if !isBasicJavaType(attribute.datatype.name)> != null ? this.${attribute.name}.toDTO() : null</#if>);
</#list>
</#if>
    }
<#else>
    /**
     * 将当前对象属性复制到DTO对象
     */
    protected void copyToDTO(${getClassName()}DTO dto) {
<#if hasDeclaredAttributes()>
<#list getDeclaredAttributes() as attribute>
        dto.set${attribute.name?cap_first}(this.${attribute.name}<#if !isBasicJavaType(attribute.datatype.name)> != null ? this.${attribute.name}.toDTO() : null</#if>);
</#list>
</#if>
    }
</#if>
}
