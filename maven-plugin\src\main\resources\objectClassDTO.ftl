package ${pkgDTO()};

<#if hasParent()>
import ${getParentClassFullNameDTO()};
</#if>
import java.io.Serializable;
import lombok.Data;
<#if hasParent()>
import lombok.EqualsAndHashCode;
</#if>

/**
* ${formatSemantics(getSemantics(), "* ")} DTO
*
* <AUTHOR>
* @program: csim
* @date ${getNow()}
*/
@Data
<#if hasParent()>
@EqualsAndHashCode(callSuper = true)
public class ${getClassName()}DTO extends ${getParentClassName()}DTO implements Serializable {
<#else>
public class ${getClassName()}DTO implements Serializable {
</#if>

<#if hasDeclaredAttributes()>
<#list getDeclaredAttributes() as attribute>
    <#if attribute.getSemantics()?? && attribute.getSemantics()?has_content>
    /**
     * ${formatSemantics(attribute.getSemantics(), "     * ")}
     */
    </#if>
    private ${getJavaTypeNameDTO(attribute.datatype.name)} ${attribute.name};

</#list>
</#if>
}
